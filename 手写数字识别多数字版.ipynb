{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 一、前期工作"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 设置GPU（如果使用的是CPU可以忽略这步）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我的环境：\n", "\n", "- 语言环境：Python3.6.5\n", "- 编译器：jupyter notebook\n", "- 深度学习环境：TensorFlow2.4.1\n", "\n", "**来自专栏：**[**《深度学习100例》**](https://blog.csdn.net/qq_38251616/category_11068756.html)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "gpus = tf.config.list_physical_devices(\"GPU\")\n", "\n", "if gpus:\n", "    gpu0 = gpus[0] #如果有多个GPU，仅使用第0个GPU\n", "    tf.config.experimental.set_memory_growth(gpu0, True) #设置GPU显存用量按需使用\n", "    tf.config.set_visible_devices([gpu0],\"GPU\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 导入数据"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models\n", "import matplotlib.pyplot as plt\n", "\n", "(train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## 3.归一化"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["((60000, 28, 28), (10000, 28, 28), (60000,), (10000,))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将像素的值标准化至0到1的区间内。\n", "train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.可视化"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1000 with 20 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(20,10))\n", "for i in range(20):\n", "    plt.subplot(5,10,i+1)\n", "    plt.xticks([])\n", "    plt.yticks([])\n", "    plt.grid(False)\n", "    plt.imshow(train_images[i], cmap=plt.cm.binary)\n", "    plt.xlabel(train_labels[i])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5.调整图片格式"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["((60000, 28, 28, 1), (10000, 28, 28, 1), (60000,), (10000,))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#调整数据到我们需要的格式\n", "train_images = train_images.reshape((60000, 28, 28, 1))\n", "test_images = test_images.reshape((10000, 28, 28, 1))\n", "\n", "# train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 二、构建CNN网络模型"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\ANACONDA\\envs\\tf-latest\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:113: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"sequential\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">26</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">26</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │           <span style=\"color: #00af00; text-decoration-color: #00af00\">320</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">11</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">11</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)     │        <span style=\"color: #00af00; text-decoration-color: #00af00\">18,496</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)       │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Flatten</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1600</span>)           │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)             │       <span style=\"color: #00af00; text-decoration-color: #00af00\">102,464</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">10</span>)             │           <span style=\"color: #00af00; text-decoration-color: #00af00\">650</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (\u001b[38;5;33mConv2D\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m26\u001b[0m, \u001b[38;5;34m26\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │           \u001b[38;5;34m320\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (\u001b[38;5;33mMaxPooling2D\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m11\u001b[0m, \u001b[38;5;34m11\u001b[0m, \u001b[38;5;34m64\u001b[0m)     │        \u001b[38;5;34m18,496\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m5\u001b[0m, \u001b[38;5;34m5\u001b[0m, \u001b[38;5;34m64\u001b[0m)       │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (\u001b[38;5;33m<PERSON><PERSON><PERSON>\u001b[0m)               │ (\u001b[38;5;45m<PERSON>one\u001b[0m, \u001b[38;5;34m1600\u001b[0m)           │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m64\u001b[0m)             │       \u001b[38;5;34m102,464\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m10\u001b[0m)             │           \u001b[38;5;34m650\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">121,930</span> (476.29 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m121,930\u001b[0m (476.29 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">121,930</span> (476.29 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m121,930\u001b[0m (476.29 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = models.Sequential([\n", "    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(28, 28, 1)),\n", "    layers.MaxPooling2D((2, 2)),\n", "    layers.Conv2D(64, (3, 3), activation='relu'),\n", "    layers.MaxPooling2D((2, 2)),\n", "    \n", "    layers.<PERSON><PERSON>(),\n", "    layers.Dense(64, activation='relu'),\n", "    layers.<PERSON><PERSON>(10)\n", "])\n", "\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 三、编译模型"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["model.compile(optimizer='adam',\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "              metrics=['accuracy'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 四、训练模型"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/3\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 5ms/step - accuracy: 0.9066 - loss: 0.3102 - val_accuracy: 0.9864 - val_loss: 0.0396\n", "Epoch 2/3\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9846 - loss: 0.0473 - val_accuracy: 0.9876 - val_loss: 0.0380\n", "Epoch 3/3\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9903 - loss: 0.0314 - val_accuracy: 0.9888 - val_loss: 0.0336\n"]}], "source": ["history = model.fit(train_images, train_labels, epochs=3, \n", "                    validation_data=(test_images, test_labels))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 五、预测"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x24b5ead5e10>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(test_images[1])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m313/313\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step\n"]}, {"data": {"text/plain": ["array([ -0.35532546,  -0.6968605 ,   7.7760534 ,  -4.4093547 ,\n", "        -6.248946  ,  -8.656315  ,   0.35807526,  -5.32223   ,\n", "        -1.0780971 , -10.846723  ], dtype=float32)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["pre = model.predict(test_images)\n", "pre[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 手写数字识别"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在准备模型...\n", "\n", "检测到已存在模型文件: model\\multidigits.keras\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n", "输入无效，请输入 'y' 或 'n'\n"]}], "source": ["import os\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models, callbacks, Input\n", "import warnings\n", "\n", "# 过滤无关警告\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"keras.src.trainers.data_adapters.py_dataset_adapter\")\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"keras.src.layers.convolutional.base_conv\")\n", "\n", "# 设置中文字体\n", "plt.rcParams[\"font.family\"] = [\"SimHei\", \"Microsoft YaHei\"]  \n", "plt.rcParams[\"axes.unicode_minus\"] = False\n", "\n", "# 模型路径\n", "MODEL_PATH = os.path.join(\"model\", \"multidigits.keras\")\n", "DISPLAY_SIZE = (600, 400)  # 统一展示尺寸\n", "OVERLAP_THRESHOLD = 0.5  # 轮廓重叠阈值，超过此值视为同一数字\n", "\n", "\n", "# --------------------------\n", "# 1. 模型创建与训练\n", "# --------------------------\n", "def create_and_train_model():\n", "    model_loaded = False\n", "    model = None\n", "    train_mean = None\n", "    \n", "    if os.path.exists(MODEL_PATH):\n", "        print(f\"\\n检测到已存在模型文件: {MODEL_PATH}\")\n", "        while True:\n", "            user_input = input(\"是否直接使用该模型进行识别？(y=使用/n=重新训练): \").strip().lower()\n", "            if user_input in ['y', 'n']:\n", "                if user_input == 'y':\n", "                    try:\n", "                        print(f\"正在加载模型 {MODEL_PATH}...\")\n", "                        model = tf.keras.models.load_model(MODEL_PATH)\n", "                        \n", "                        # 加载测试数据评估\n", "                        (_, _), (test_images, test_labels) = datasets.mnist.load_data()\n", "                        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0\n", "                        train_mean = np.mean(test_images)\n", "                        test_images -= train_mean\n", "                        \n", "                        print(\"正在评估模型性能...\")\n", "                        test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)\n", "                        print(f'\\n已加载模型在测试集上的准确率: {test_acc:.4f}')\n", "                        model_loaded = True\n", "                        break\n", "                    except Exception as e:\n", "                        print(f\"加载模型失败: {str(e)}\")\n", "                        retry = input(\"是否尝试重新加载？(y/n): \").strip().lower()\n", "                        if retry != 'y':\n", "                            print(\"将进行模型重新训练\")\n", "                            break\n", "                else:\n", "                    print(\"用户选择重新训练模型\")\n", "                    break\n", "            else:\n", "                print(\"输入无效，请输入 'y' 或 'n'\")\n", "    \n", "    if not model_loaded:\n", "        os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "        print(f\"\\n开始训练新模型，保存至 {MODEL_PATH}\")\n", "        \n", "        # 加载MNIST数据集\n", "        (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()\n", "        train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0\n", "        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0\n", "        train_mean = np.mean(train_images)\n", "        train_images -= train_mean\n", "        test_images -= train_mean\n", "        \n", "        # 增强易混淆数字样本\n", "        def augment_critical_digits(images, labels, target_digits, multiply=2):\n", "            for digit in target_digits:\n", "                mask = labels == digit\n", "                target_imgs = images[mask]\n", "                target_lbls = labels[mask]\n", "                \n", "                datagen = tf.keras.preprocessing.image.ImageDataGenerator(\n", "                    rotation_range=10,\n", "                    width_shift_range=0.15,\n", "                    height_shift_range=0.15,\n", "                    zoom_range=0.2,\n", "                    shear_range=0.15\n", "                )\n", "                augmented = next(datagen.flow(target_imgs, target_lbls, batch_size=len(target_imgs), shuffle=False))\n", "                \n", "                for _ in range(multiply-1):\n", "                    images = np.concatenate([images, augmented[0]])\n", "                    labels = np.concatenate([labels, augmented[1]])\n", "            return images, labels\n", "        \n", "        # 增强所有数字的样本，提高泛化能力\n", "        train_images, train_labels = augment_critical_digits(train_images, train_labels, list(range(10)), multiply=2)\n", "        \n", "        # 通用数据增强\n", "        datagen = tf.keras.preprocessing.image.ImageDataGenerator(\n", "            rotation_range=15,\n", "            width_shift_range=0.2,\n", "            height_shift_range=0.2,\n", "            zoom_range=0.25,\n", "            shear_range=0.2,\n", "            fill_mode='constant',\n", "            cval=0.0\n", "        )\n", "        datagen.fit(train_images)\n", "        \n", "        # 模型结构\n", "        model = models.Sequential([\n", "            Input(shape=(28, 28, 1)),\n", "            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "            layers.BatchNormalization(),\n", "            layers.Conv2D(64, (3, 3), activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "            layers.BatchNormalization(),\n", "            layers.Conv2D(128, (3, 3), activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.<PERSON><PERSON>(),\n", "            layers.Dense(256, activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.Dropout(0.4),\n", "            layers.Dense(128, activation='relu'),\n", "            layers.Dropout(0.3),\n", "            layers.<PERSON><PERSON>(10)\n", "        ])\n", "        \n", "        # 编译与训练\n", "        optimizer = tf.keras.optimizers.<PERSON>(learning_rate=0.001, weight_decay=1e-5)\n", "        model.compile(\n", "            optimizer=optimizer,\n", "            loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "            metrics=['accuracy']\n", "        )\n", "        \n", "        callback_list = [\n", "            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=3),\n", "            callbacks.EarlyStopping(monitor='val_accuracy', patience=8, restore_best_weights=True),\n", "            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)\n", "        ]\n", "        \n", "        print(\"\\n开始训练新模型...\")\n", "        model.fit(\n", "            datagen.flow(train_images, train_labels, batch_size=64),\n", "            epochs=15,\n", "            validation_data=(test_images, test_labels),\n", "            callbacks=callback_list\n", "        )\n", "        \n", "        if os.path.exists(MODEL_PATH):\n", "            model = tf.keras.models.load_model(MODEL_PATH)\n", "            test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)\n", "            print(f'\\n模型在测试集上的最佳准确率: {test_acc:.4f}')\n", "        else:\n", "            print(\"\\n警告：未找到最佳模型文件，使用最后训练的模型\")\n", "    \n", "    return model, train_mean\n", "\n", "\n", "# --------------------------\n", "# 辅助函数：计算两个矩形的重叠率\n", "# --------------------------\n", "def calculate_overlap(rect1, rect2):\n", "    \"\"\"\n", "    计算两个矩形的重叠率\n", "    rect: (x, y, w, h)\n", "    \"\"\"\n", "    x1, y1, w1, h1 = rect1\n", "    x2, y2, w2, h2 = rect2\n", "    \n", "    # 计算交集区域\n", "    x_left = max(x1, x2)\n", "    y_top = max(y1, y2)\n", "    x_right = min(x1 + w1, x2 + w2)\n", "    y_bottom = min(y1 + h1, y2 + h2)\n", "    \n", "    # 如果没有交集\n", "    if x_right < x_left or y_bottom < y_top:\n", "        return 0.0\n", "    \n", "    # 计算交集面积\n", "    intersection_area = (x_right - x_left) * (y_bottom - y_top)\n", "    \n", "    # 计算两个矩形的面积\n", "    area1 = w1 * h1\n", "    area2 = w2 * h2\n", "    \n", "    # 计算重叠率（取较小面积的比例）\n", "    return intersection_area / min(area1, area2)\n", "\n", "\n", "# --------------------------\n", "# 2. 图片预处理与数字分割（重点解决重复检测）\n", "# --------------------------\n", "def load_and_preprocess_multidigit_images(directory, train_mean):\n", "    images = []           # 预处理后的数字图像\n", "    original_images = []  # 原始图像\n", "    processed_images = [] # 框选后的图像\n", "    filenames = []        # 文件名\n", "    digit_rois = []       # 分割出的数字ROI\n", "    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')\n", "    \n", "    if not os.path.exists(directory):\n", "        print(f\"错误：目录 '{directory}' 不存在！\")\n", "        return [], [], [], [], []\n", "    \n", "    try:\n", "        all_files = os.listdir(directory)\n", "    except UnicodeDecodeError:\n", "        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))\n", "    \n", "    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]\n", "    print(f\"找到图片文件：{image_files}\")\n", "\n", "    for filename in image_files:\n", "        img_path = os.path.join(directory, filename)\n", "        \n", "        try:\n", "            # 读取图片（支持中文路径）\n", "            img_data = np.fromfile(img_path, dtype=np.uint8)\n", "            img = cv2.imdecode(img_data, cv2.IMREAD_COLOR)\n", "            if img is None:\n", "                print(f\"警告：{filename} 无法解码为图像\")\n", "                continue\n", "        except Exception as e:\n", "            print(f\"无法读取 {filename}：{str(e)}\")\n", "            continue\n", "        \n", "        # 保存原始图片（保持比例缩放）\n", "        h, w = img.shape[:2]\n", "        scale = min(DISPLAY_SIZE[0]/w, DISPLAY_SIZE[1]/h)\n", "        new_w, new_h = int(w * scale), int(h * scale)\n", "        resized_img = cv2.resize(img, (new_w, new_h))\n", "        padded_img = np.zeros((DISPLAY_SIZE[1], DISPLAY_SIZE[0], 3), dtype=np.uint8)\n", "        y_offset, x_offset = (DISPLAY_SIZE[1]-new_h)//2, (DISPLAY_SIZE[0]-new_w)//2\n", "        padded_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized_img\n", "        original_images.append(padded_img.copy())  # 保存原始图\n", "        \n", "        # 转换为灰度图\n", "        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)\n", "        \n", "        # 多阈值处理策略\n", "        thresholds = []\n", "        \n", "        # 方法1：OTSU阈值\n", "        _, thresh1 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)\n", "        thresholds.append(thresh1)\n", "        \n", "        # 方法2：自适应阈值（适合光照不均）\n", "        thresh2 = cv2.adaptiveThreshold(\n", "            cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(gray, (5, 5), 0), 255,\n", "            cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2\n", "        )\n", "        thresholds.append(thresh2)\n", "        \n", "        # 合并所有可能的轮廓\n", "        all_contours = []\n", "        for thresh in thresholds:\n", "            # 形态学操作\n", "            kernel = np.ones((2, 2), np.uint8)\n", "            thresh = cv2.erode(thresh, kernel, iterations=1)\n", "            thresh = cv2.dilate(thresh, kernel, iterations=2)\n", "            \n", "            # 查找轮廓\n", "            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "            all_contours.extend(contours)\n", "        \n", "        # 轮廓筛选与去重（核心修复）\n", "        digit_contours = []\n", "        img_h, img_w = gray.shape\n", "        min_area = max(30, img_h * img_w * 0.0005)\n", "        max_area = img_h * img_w * 0.5\n", "        \n", "        # 初步筛选轮廓\n", "        candidate_contours = []\n", "        for cnt in all_contours:\n", "            area = cv2.contourArea(cnt)\n", "            x, y, w_cnt, h_cnt = cv2.boundingRect(cnt)\n", "            \n", "            if (min_area < area < max_area and \n", "                0.2 < w_cnt/h_cnt < 2.0 and\n", "                x >= 0 and y >= 0 and \n", "                x + w_cnt <= img_w and \n", "                y + h_cnt <= img_h):\n", "                candidate_contours.append((x, y, w_cnt, h_cnt, area))\n", "        \n", "        # 关键修复：按面积排序，优先保留大面积轮廓，去除重叠轮廓\n", "        if candidate_contours:\n", "            # 按面积降序排序（优先保留大轮廓）\n", "            candidate_contours.sort(key=lambda c: c[4], reverse=True)\n", "            \n", "            # 去重处理\n", "            unique_contours = []\n", "            for cnt in candidate_contours:\n", "                x, y, w_cnt, h_cnt, area = cnt\n", "                current_rect = (x, y, w_cnt, h_cnt)\n", "                is_duplicate = False\n", "                \n", "                # 与已保留的轮廓比较，检查是否重叠\n", "                for unique_rect in unique_contours:\n", "                    overlap = calculate_overlap(current_rect, unique_rect)\n", "                    if overlap > OVERLAP_THRESHOLD:\n", "                        is_duplicate = True\n", "                        break\n", "                \n", "                if not is_duplicate:\n", "                    unique_contours.append(current_rect)\n", "            \n", "            # 按x坐标排序\n", "            unique_contours.sort(key=lambda c: c[0])\n", "            digit_contours = unique_contours\n", "        \n", "        # 处理每个数字轮廓\n", "        current_digits = []\n", "        current_rois = []\n", "        scale_x, scale_y = new_w / img_w, new_h / img_h  # 缩放比例\n", "        \n", "        for i, (x, y, w_cnt, h_cnt) in enumerate(digit_contours):\n", "            # 绘制框选（在展示图上）\n", "            x_scaled = int(x * scale_x) + x_offset\n", "            y_scaled = int(y * scale_y) + y_offset\n", "            w_scaled = int(w_cnt * scale_x)\n", "            h_scaled = int(h_cnt * scale_y)\n", "            \n", "            padding = 3\n", "            x_scaled = max(0, x_scaled - padding)\n", "            y_scaled = max(0, y_scaled - padding)\n", "            w_scaled = min(padded_img.shape[1] - x_scaled, w_scaled + 2 * padding)\n", "            h_scaled = min(padded_img.shape[0] - y_scaled, h_scaled + 2 * padding)\n", "            \n", "            cv2.rectangle(padded_img, (x_scaled, y_scaled), \n", "                         (x_scaled + w_scaled, y_scaled + h_scaled), (0, 255, 0), 2)\n", "            cv2.putText(padded_img, f\"{i+1}\", (x_scaled, y_scaled - 5), \n", "                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)\n", "            \n", "            # 提取原始ROI\n", "            x_original = max(0, x - padding)\n", "            y_original = max(0, y - padding)\n", "            w_original = min(img_w - x_original, w_cnt + 2 * padding)\n", "            h_original = min(img_h - y_original, h_cnt + 2 * padding)\n", "            \n", "            # 对ROI单独处理，提高对比度\n", "            roi_gray = gray[y_original:y_original+h_original, x_original:x_original+w_original]\n", "            roi_gray = cv2.equalizeHist(roi_gray)\n", "            _, roi_thresh = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)\n", "            \n", "            # 调整为正方形\n", "            roi_h, roi_w = roi_thresh.shape\n", "            max_dim = max(roi_h, roi_w)\n", "            square_roi = np.zeros((max_dim, max_dim), dtype=np.uint8)\n", "            y_offset_roi = (max_dim - roi_h) // 2\n", "            x_offset_roi = (max_dim - roi_w) // 2\n", "            square_roi[y_offset_roi:y_offset_roi+roi_h, x_offset_roi:x_offset_roi+roi_w] = roi_thresh\n", "            \n", "            # 调整为28x28\n", "            resized = cv2.resize(square_roi, (28, 28), interpolation=cv2.INTER_AREA)\n", "            \n", "            # 归一化\n", "            normalized = resized / 255.0 - train_mean\n", "            \n", "            current_digits.append(normalized)\n", "            current_rois.append(cv2.resize(square_roi, (100, 100), interpolation=cv2.INTER_AREA))\n", "        \n", "        processed_images.append(padded_img)\n", "        \n", "        # 保存结果\n", "        if current_digits:\n", "            images.append(current_digits)\n", "            digit_rois.append(current_rois)\n", "            filenames.append(filename)\n", "            print(f\"成功处理 {filename}，分割出 {len(current_digits)} 个数字\")\n", "        else:\n", "            # 兜底机制：整个图片作为一个数字处理\n", "            print(f\"警告：在 {filename} 中未检测到数字，尝试整体识别...\")\n", "            \n", "            # 将整个图片作为一个数字处理\n", "            gray_resized = cv2.resize(gray, (28, 28), interpolation=cv2.INTER_AREA)\n", "            _, thresh = cv2.threshold(gray_resized, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)\n", "            normalized = thresh / 255.0 - train_mean\n", "            \n", "            current_digits = [normalized]\n", "            current_rois = [cv2.resize(thresh, (100, 100), interpolation=cv2.INTER_AREA)]\n", "            \n", "            images.append(current_digits)\n", "            digit_rois.append(current_rois)\n", "            filenames.append(filename)\n", "            print(f\"已将 {filename} 作为单个数字处理\")\n", "\n", "    return images, original_images, processed_images, digit_rois, filenames\n", "\n", "\n", "# --------------------------\n", "# 3. 预测函数\n", "# --------------------------\n", "def predict_multidigits(model, images):\n", "    all_probabilities = []\n", "    all_predictions = []\n", "    all_confidences = []\n", "    \n", "    for digits in images:\n", "        if not digits:\n", "            all_probabilities.append([])\n", "            all_predictions.append([])\n", "            all_confidences.append([])\n", "            continue\n", "            \n", "        input_data = np.array(digits).reshape(-1, 28, 28, 1)\n", "        logits = model.predict(input_data, verbose=0)\n", "        \n", "        def softmax(x):\n", "            x_max = np.max(x, axis=1, keepdims=True)\n", "            e_x = np.exp(x - x_max)\n", "            return e_x / e_x.sum(axis=1, keepdims=True)\n", "        \n", "        probabilities = softmax(logits)\n", "        predictions = np.argmax(probabilities, axis=1)\n", "        confidences = np.max(probabilities, axis=1) * 100\n", "        \n", "        all_probabilities.append(probabilities)\n", "        all_predictions.append(predictions)\n", "        all_confidences.append(confidences)\n", "    \n", "    return all_probabilities, all_predictions, all_confidences\n", "\n", "\n", "# --------------------------\n", "# 4. 可视化展示结果\n", "# --------------------------\n", "def visualize_results(original_images, processed_images, digit_rois, filenames, \n", "                     predictions, confidences, probabilities):\n", "    for i in range(len(filenames)):\n", "        print(f\"\\n===== 图片 {filenames[i]} 分析结果 =====\")\n", "        \n", "        # 显示原始图片和框选结果\n", "        plt.figure(figsize=(15, 6))\n", "        plt.subplot(2, 1, 1)\n", "        plt.imshow(cv2.cvtColor(original_images[i], cv2.COLOR_BGR2RGB))\n", "        plt.title(f\"原始图片：{filenames[i]}\")\n", "        plt.axis('off')\n", "        \n", "        plt.subplot(2, 1, 2)\n", "        plt.imshow(cv2.cvtColor(processed_images[i], cv2.COLOR_BGR2RGB))\n", "        plt.title(f\"数字检测结果：共检测到 {len(predictions[i])} 个数字\")\n", "        plt.axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 显示每个数字的识别结果\n", "        if len(predictions[i]) > 0:\n", "            combined_result = ''.join(map(str, predictions[i]))\n", "            print(f\"组合识别结果：{combined_result}\")\n", "            \n", "            # 创建子图展示每个数字的识别详情\n", "            fig, axes = plt.subplots(1, len(predictions[i]), figsize=(3*len(predictions[i]), 5))\n", "            if len(predictions[i]) == 1:\n", "                axes = [axes]\n", "                \n", "            for j, ax in enumerate(axes):\n", "                ax.imshow(digit_rois[i][j], cmap='gray')\n", "                ax.set_title(f\"预测：{predictions[i][j]}\\n置信度：{confidences[i][j]:.1f}%\")\n", "                ax.axis('off')\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "            \n", "            # 打印详细概率分布\n", "            for j in range(len(predictions[i])):\n", "                print(f\"\\n数字 {j+1} 分析：\")\n", "                print(f\"预测结果：{predictions[i][j]}（置信度：{confidences[i][j]:.1f}%）\")\n", "                top2 = np.argsort(probabilities[i][j])[-2:][::-1]\n", "                print(f\"Top 2 可能的数字：{top2[0]}（{probabilities[i][j][top2[0]]*100:.1f}%）、{top2[1]}（{probabilities[i][j][top2[1]]*100:.1f}%）\")\n", "            \n", "            print(\"\\n\" + \"-\" * 60)\n", "\n", "\n", "# --------------------------\n", "# 5. 主程序\n", "# --------------------------\n", "def main():\n", "    print(\"正在准备模型...\")\n", "    model, train_mean = create_and_train_model()\n", "    \n", "    image_dir = \"numbers\"  # 存放图片的文件夹\n", "    images, original_images, processed_images, digit_rois, filenames = load_and_preprocess_multidigit_images(image_dir, train_mean)\n", "\n", "    if len(images) > 0:\n", "        probabilities, predicted_digits, confidence = predict_multidigits(model, images)\n", "        visualize_results(original_images, processed_images, digit_rois, filenames, \n", "                         predicted_digits, confidence, probabilities)\n", "    else:\n", "        print(\"没有找到可处理的图片文件\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "    "]}], "metadata": {"kernelspec": {"display_name": "tf-latest", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "259.475px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 4}