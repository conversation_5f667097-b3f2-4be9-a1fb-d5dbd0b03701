{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 一、前期工作"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 设置GPU（如果使用的是CPU可以忽略这步）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我的环境：\n", "\n", "- 语言环境：Python3.6.5\n", "- 编译器：jupyter notebook\n", "- 深度学习环境：TensorFlow2.4.1\n", "\n", "**来自专栏：**[**《深度学习100例》**](https://blog.csdn.net/qq_38251616/category_11068756.html)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "gpus = tf.config.list_physical_devices(\"GPU\")\n", "\n", "if gpus:\n", "    gpu0 = gpus[0] #如果有多个GPU，仅使用第0个GPU\n", "    tf.config.experimental.set_memory_growth(gpu0, True) #设置GPU显存用量按需使用\n", "    tf.config.set_visible_devices([gpu0],\"GPU\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 导入数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models\n", "import matplotlib.pyplot as plt\n", "\n", "(train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## 3.归一化"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["((60000, 28, 28), (10000, 28, 28), (60000,), (10000,))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将像素的值标准化至0到1的区间内。\n", "train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.可视化"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1000 with 20 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(20,10))\n", "for i in range(20):\n", "    plt.subplot(5,10,i+1)\n", "    plt.xticks([])\n", "    plt.yticks([])\n", "    plt.grid(False)\n", "    plt.imshow(train_images[i], cmap=plt.cm.binary)\n", "    plt.xlabel(train_labels[i])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5.调整图片格式"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["((60000, 28, 28, 1), (10000, 28, 28, 1), (60000,), (10000,))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#调整数据到我们需要的格式\n", "train_images = train_images.reshape((60000, 28, 28, 1))\n", "test_images = test_images.reshape((10000, 28, 28, 1))\n", "\n", "# train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 二、构建CNN网络模型"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\ANACONDA\\envs\\tf-latest\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:113: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"sequential\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">26</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">26</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │           <span style=\"color: #00af00; text-decoration-color: #00af00\">320</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">11</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">11</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)     │        <span style=\"color: #00af00; text-decoration-color: #00af00\">18,496</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)       │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Flatten</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1600</span>)           │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)             │       <span style=\"color: #00af00; text-decoration-color: #00af00\">102,464</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">10</span>)             │           <span style=\"color: #00af00; text-decoration-color: #00af00\">650</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (\u001b[38;5;33mConv2D\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m26\u001b[0m, \u001b[38;5;34m26\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │           \u001b[38;5;34m320\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (\u001b[38;5;33mMaxPooling2D\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m11\u001b[0m, \u001b[38;5;34m11\u001b[0m, \u001b[38;5;34m64\u001b[0m)     │        \u001b[38;5;34m18,496\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m5\u001b[0m, \u001b[38;5;34m5\u001b[0m, \u001b[38;5;34m64\u001b[0m)       │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (\u001b[38;5;33m<PERSON><PERSON><PERSON>\u001b[0m)               │ (\u001b[38;5;45m<PERSON>one\u001b[0m, \u001b[38;5;34m1600\u001b[0m)           │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m64\u001b[0m)             │       \u001b[38;5;34m102,464\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m10\u001b[0m)             │           \u001b[38;5;34m650\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">121,930</span> (476.29 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m121,930\u001b[0m (476.29 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">121,930</span> (476.29 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m121,930\u001b[0m (476.29 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = models.Sequential([\n", "    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(28, 28, 1)),\n", "    layers.MaxPooling2D((2, 2)),\n", "    layers.Conv2D(64, (3, 3), activation='relu'),\n", "    layers.MaxPooling2D((2, 2)),\n", "    \n", "    layers.<PERSON><PERSON>(),\n", "    layers.Dense(64, activation='relu'),\n", "    layers.<PERSON><PERSON>(10)\n", "])\n", "\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 三、编译模型"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["model.compile(optimizer='adam',\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "              metrics=['accuracy'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 四、训练模型"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m13s\u001b[0m 6ms/step - accuracy: 0.9007 - loss: 0.3177 - val_accuracy: 0.9802 - val_loss: 0.0576\n", "Epoch 2/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m12s\u001b[0m 6ms/step - accuracy: 0.9843 - loss: 0.0500 - val_accuracy: 0.9852 - val_loss: 0.0446\n", "Epoch 3/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9903 - loss: 0.0321 - val_accuracy: 0.9896 - val_loss: 0.0309\n", "Epoch 4/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9929 - loss: 0.0226 - val_accuracy: 0.9893 - val_loss: 0.0302\n", "Epoch 5/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9949 - loss: 0.0153 - val_accuracy: 0.9898 - val_loss: 0.0333\n", "Epoch 6/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9961 - loss: 0.0113 - val_accuracy: 0.9917 - val_loss: 0.0290\n", "Epoch 7/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9964 - loss: 0.0108 - val_accuracy: 0.9905 - val_loss: 0.0319\n", "Epoch 8/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9977 - loss: 0.0070 - val_accuracy: 0.9863 - val_loss: 0.0496\n", "Epoch 9/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9975 - loss: 0.0082 - val_accuracy: 0.9916 - val_loss: 0.0332\n", "Epoch 10/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9985 - loss: 0.0058 - val_accuracy: 0.9889 - val_loss: 0.0401\n"]}], "source": ["history = model.fit(train_images, train_labels, epochs=10, \n", "                    validation_data=(test_images, test_labels))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 五、预测"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x243327b9810>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(test_images[1])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m313/313\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 3ms/step\n"]}, {"data": {"text/plain": ["array([-10.095278 ,  -2.875912 ,  26.037603 ,  -9.978585 ,  -4.776897 ,\n", "       -25.725517 ,  -1.135879 ,  -3.8313267, -12.670046 , -10.592291 ],\n", "      dtype=float32)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pre = model.predict(test_images)\n", "pre[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 手写数字识别"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在准备模型...\n", "\n", "检测到已存在模型文件: model\\onedigit.keras\n", "正在加载模型 model\\onedigit.keras...\n", "正在评估模型性能...\n", "313/313 - 3s - 8ms/step - accuracy: 0.9883 - loss: 0.0373\n", "\n", "已加载模型在测试集上的准确率: 0.9883\n", "找到图片文件：['IMG_20250725_000016.jpg', 'QQ截图20250727004633.png', 'QQ截图20250727004640.png', 'QQ截图20250727004652.png']\n", "成功处理：IMG_20250725_000016.jpg\n", "成功处理：QQ截图20250727004633.png\n", "成功处理：QQ截图20250727004640.png\n", "成功处理：QQ截图20250727004652.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 IMG_20250725_000016.jpg 分析：\n", "预测数字：5（置信度：80.0%）\n", "Top 2 可能的数字：5（80.0%）、8（10.8%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 QQ截图20250727004633.png 分析：\n", "预测数字：6（置信度：100.0%）\n", "Top 2 可能的数字：6（100.0%）、5（0.0%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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******************************************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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 QQ截图20250727004640.png 分析：\n", "预测数字：1（置信度：100.0%）\n", "Top 2 可能的数字：1（100.0%）、7（0.0%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 QQ截图20250727004652.png 分析：\n", "预测数字：9（置信度：99.9%）\n", "Top 2 可能的数字：9（99.9%）、8（0.1%）\n", "------------------------------------------------------------\n"]}], "source": ["import os\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models, callbacks, Input\n", "import warnings\n", "\n", "# 过滤无关警告\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"keras.src.trainers.data_adapters.py_dataset_adapter\")\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"keras.src.layers.convolutional.base_conv\")\n", "\n", "# 设置中文字体（确保图表中文正常显示）\n", "plt.rcParams[\"font.family\"] = [\"SimHei\", \"Microsoft YaHei\"]  \n", "plt.rcParams[\"axes.unicode_minus\"] = False  # 解决负号显示问题\n", "\n", "# 模型保存路径\n", "MODEL_PATH = os.path.join(\"model\", \"onedigit.keras\")\n", "\n", "# --------------------------\n", "# 1. 模型创建与训练\n", "# --------------------------\n", "def create_and_train_model():\n", "    # 首先检查是否存在已训练的模型\n", "    model_loaded = False\n", "    model = None\n", "    train_mean = None\n", "    \n", "    # 检查模型文件是否存在\n", "    if os.path.exists(MODEL_PATH):\n", "        print(f\"\\n检测到已存在模型文件: {MODEL_PATH}\")\n", "        # 使用循环确保获取有效的用户输入\n", "        while True:\n", "            user_input = input(\"是否直接使用该模型进行识别？(y=使用/n=重新训练): \").strip().lower()\n", "            if user_input in ['y', 'n']:\n", "                if user_input == 'y':\n", "                    try:\n", "                        print(f\"正在加载模型 {MODEL_PATH}...\")\n", "                        model = tf.keras.models.load_model(MODEL_PATH)\n", "                        \n", "                        # 加载测试数据评估模型\n", "                        (_, _), (test_images, test_labels) = datasets.mnist.load_data()\n", "                        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0\n", "                        train_mean = np.mean(test_images)  # 使用测试集均值作为参考\n", "                        test_images -= train_mean\n", "                        \n", "                        print(\"正在评估模型性能...\")\n", "                        test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)\n", "                        print(f'\\n已加载模型在测试集上的准确率: {test_acc:.4f}')\n", "                        model_loaded = True\n", "                        break\n", "                    except Exception as e:\n", "                        print(f\"加载模型失败: {str(e)}\")\n", "                        retry = input(\"是否尝试重新加载？(y/n): \").strip().lower()\n", "                        if retry != 'y':\n", "                            print(\"将进行模型重新训练\")\n", "                            break\n", "                else:  # 用户选择重新训练\n", "                    print(\"用户选择重新训练模型\")\n", "                    break\n", "            else:\n", "                print(\"输入无效，请输入 'y' 或 'n'\")\n", "    \n", "    # 如果没有成功加载模型，则进行训练\n", "    if not model_loaded:\n", "        # 确保模型保存目录存在\n", "        os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "        \n", "        # 加载MNIST数据集\n", "        (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()\n", "        \n", "        # 数据预处理\n", "        train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0\n", "        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0\n", "        train_mean = np.mean(train_images)\n", "        train_images -= train_mean\n", "        test_images -= train_mean\n", "        \n", "        # 增强易混淆数字的样本\n", "        def augment_critical_digits(images, labels, target_digits, multiply=2):\n", "            \"\"\"针对性增强样本，提升识别准确率\"\"\"\n", "            for digit in target_digits:\n", "                mask = labels == digit\n", "                target_imgs = images[mask]\n", "                target_lbls = labels[mask]\n", "                \n", "                # 生成多样化增强样本\n", "                datagen = tf.keras.preprocessing.image.ImageDataGenerator(\n", "                    rotation_range=8,\n", "                    width_shift_range=0.1,\n", "                    height_shift_range=0.1,\n", "                    zoom_range=0.15\n", "                )\n", "                augmented = next(datagen.flow(target_imgs, target_lbls, batch_size=len(target_imgs), shuffle=False))\n", "                \n", "                # 扩展样本\n", "                for _ in range(multiply-1):\n", "                    images = np.concatenate([images, augmented[0]])\n", "                    labels = np.concatenate([labels, augmented[1]])\n", "            return images, labels\n", "        \n", "        # 增强7和9的样本\n", "        train_images, train_labels = augment_critical_digits(train_images, train_labels, [9, 7])\n", "        \n", "        # 通用数据增强\n", "        datagen = tf.keras.preprocessing.image.ImageDataGenerator(\n", "            rotation_range=12,\n", "            width_shift_range=0.15,\n", "            height_shift_range=0.15,\n", "            zoom_range=0.2,\n", "            shear_range=0.1,\n", "            fill_mode='constant',\n", "            cval=0.0\n", "        )\n", "        datagen.fit(train_images)\n", "        \n", "        # 模型结构\n", "        model = models.Sequential([\n", "            Input(shape=(28, 28, 1)),  # 明确的输入层定义\n", "            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "            layers.Conv2D(64, (1, 3), activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "            layers.Conv2D(128, (3, 1), activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.<PERSON><PERSON>(),\n", "            layers.Dense(128, activation='relu'),\n", "            layers.Dropout(0.3),\n", "            layers.<PERSON><PERSON>(10)\n", "        ])\n", "        \n", "        # 编译模型\n", "        optimizer = tf.keras.optimizers.Adam(\n", "            learning_rate=0.001,\n", "            weight_decay=1e-5\n", "        )\n", "        model.compile(\n", "            optimizer=optimizer,\n", "            loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "            metrics=['accuracy']\n", "        )\n", "        \n", "        # 训练回调\n", "        callback_list = [\n", "            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=3),\n", "            callbacks.EarlyStopping(monitor='val_accuracy', patience=6, restore_best_weights=True),\n", "            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)\n", "        ]\n", "        \n", "        # 训练模型\n", "        print(\"\\n开始训练新模型...\")\n", "        model.fit(\n", "            datagen.flow(train_images, train_labels, batch_size=64),\n", "            epochs=5,  # 可根据需要调整训练轮数\n", "            validation_data=(test_images, test_labels),\n", "            callbacks=callback_list\n", "        )\n", "        \n", "        # 加载最佳模型\n", "        if os.path.exists(MODEL_PATH):\n", "            model = tf.keras.models.load_model(MODEL_PATH)\n", "            test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)\n", "            print(f'\\n模型在测试集上的最佳准确率: {test_acc:.4f}')\n", "        else:\n", "            print(\"\\n警告：未找到最佳模型文件，使用最后训练的模型\")\n", "    \n", "    return model, train_mean\n", "\n", "# --------------------------\n", "# 2. 图片预处理（兼容中文文件名）\n", "# --------------------------\n", "def load_and_preprocess_images(directory, train_mean):\n", "    images = []\n", "    original_images = []\n", "    processed_images = []\n", "    filenames = []\n", "    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')\n", "    \n", "    if not os.path.exists(directory):\n", "        print(f\"错误：目录 '{directory}' 不存在！\")\n", "        return np.array([]), np.array([]), np.array([]), []\n", "    \n", "    # 读取目录下所有文件，确保中文文件名编码正确\n", "    try:\n", "        all_files = os.listdir(directory)\n", "    except UnicodeDecodeError:\n", "        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))\n", "    \n", "    # 筛选图片文件（支持中文文件名）\n", "    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]\n", "    print(f\"找到图片文件：{image_files}\")\n", "\n", "    for filename in image_files:\n", "        img_path = os.path.join(directory, filename)\n", "        \n", "        try:\n", "            # 读取图片（支持中文路径）\n", "            img_data = np.fromfile(img_path, dtype=np.uint8)\n", "            img = cv2.imdecode(img_data, cv2.IMREAD_GRAYSCALE)\n", "        except Exception as e:\n", "            print(f\"无法读取 {filename}：{str(e)}\")\n", "            continue\n", "        \n", "        if img is None:\n", "            print(f\"警告：{filename} 可能损坏或格式不支持\")\n", "            continue\n", "        \n", "        # 保存原始图片（缩放后便于显示）\n", "        original_images.append(cv2.resize(img, (200, 200)))\n", "        \n", "        # 预处理流程\n", "        img_scaled = cv2.resize(img, (56, 56), interpolation=cv2.INTER_CUBIC)\n", "        img_resized = cv2.resize(img_scaled, (28, 28))\n", "        \n", "        _, img_thresh = cv2.threshold(\n", "            img_resized, 80, 255,\n", "            cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU\n", "        )\n", "        \n", "        kernel = np.ones((1, 1), np.uint8)\n", "        img_clean = cv2.morphologyEx(img_thresh, cv2.MORPH_OPEN, kernel)\n", "        \n", "        contours, _ = cv2.findContours(img_clean, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "        if contours:\n", "            max_contour = max(contours, key=cv2.contourArea)\n", "            x, y, w, h = cv2.boundingRect(max_contour)\n", "            digit_roi = img_clean[y:y+h, x:x+w]\n", "            \n", "            scale = min(22/w, 22/h)\n", "            resized_digit = cv2.resize(digit_roi, (int(w*scale), int(h*scale)))\n", "            padded_digit = np.zeros((28, 28), dtype=np.uint8)\n", "            y_offset = (28 - resized_digit.shape[0]) // 2\n", "            x_offset = (28 - resized_digit.shape[1]) // 2\n", "            padded_digit[y_offset:y_offset+resized_digit.shape[0], \n", "                         x_offset:x_offset+resized_digit.shape[1]] = resized_digit\n", "        else:\n", "            padded_digit = img_clean\n", "        \n", "        normalized_img = padded_digit / 255.0 - train_mean\n", "        processed_images.append(padded_digit)\n", "        images.append(normalized_img)\n", "        filenames.append(filename)\n", "        print(f\"成功处理：{filename}\")\n", "\n", "    return np.array(images), np.array(original_images), np.array(processed_images), filenames\n", "\n", "# --------------------------\n", "# 3. 预测函数\n", "# --------------------------\n", "def predict_digits(model, images):\n", "    logits = model.predict(images, verbose=0)\n", "    \n", "    def softmax(x):\n", "        \"\"\"数值稳定的softmax实现\"\"\"\n", "        x_max = np.max(x, axis=1, keepdims=True)\n", "        e_x = np.exp(x - x_max)\n", "        return e_x / e_x.sum(axis=1, keepdims=True)\n", "    \n", "    probabilities = softmax(logits)\n", "    return probabilities, np.argmax(probabilities, axis=1), np.max(probabilities, axis=1)*100\n", "\n", "# --------------------------\n", "# 4. 主程序\n", "# --------------------------\n", "def main():\n", "    print(\"正在准备模型...\")\n", "    model, train_mean = create_and_train_model()\n", "    \n", "    image_dir = \"numbers\"  # 存放图片的文件夹（支持中文路径）\n", "    images, original_images, processed_images, filenames = load_and_preprocess_images(image_dir, train_mean)\n", "\n", "    if len(images) > 0:\n", "        model_input = images.reshape(-1, 28, 28, 1)\n", "        probabilities, predicted_digits, confidence = predict_digits(model, model_input)\n", "        \n", "        for i in range(len(images)):\n", "            plt.figure(figsize=(12, 4))\n", "            plt.subplot(131)\n", "            plt.imshow(original_images[i], cmap='gray')\n", "            plt.title(f\"原始图片：{filenames[i]}\")\n", "            plt.axis('off')\n", "            \n", "            plt.subplot(132)\n", "            plt.imshow(processed_images[i], cmap='gray')\n", "            plt.title(\"处理后（模型输入）\")\n", "            plt.axis('off')\n", "            \n", "            plt.subplot(133)\n", "            plt.bar(range(10), probabilities[i], color='skyblue')\n", "            plt.xticks(range(10))\n", "            plt.title(f\"预测：{predicted_digits[i]}（置信度：{confidence[i]:.1f}%）\")\n", "            plt.xlabel(\"数字\")\n", "            plt.ylabel(\"概率\")\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "            \n", "            # 打印结果\n", "            print(f\"\\n图片 {filenames[i]} 分析：\")\n", "            print(f\"预测数字：{predicted_digits[i]}（置信度：{confidence[i]:.1f}%）\")\n", "            top2 = np.argsort(probabilities[i])[-2:][::-1]\n", "            print(f\"Top 2 可能的数字：{top2[0]}（{probabilities[i][top2[0]]*100:.1f}%）、{top2[1]}（{probabilities[i][top2[1]]*100:.1f}%）\")\n", "            print(\"-\" * 60)\n", "    else:\n", "        print(\"没有找到可处理的图片文件\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"kernelspec": {"display_name": "tf-latest", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "259.475px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 4}