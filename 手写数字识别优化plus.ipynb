import tensorflow as tf
gpus = tf.config.list_physical_devices("GPU")

if gpus:
    gpu0 = gpus[0] #如果有多个GPU，仅使用第0个GPU
    tf.config.experimental.set_memory_growth(gpu0, True) #设置GPU显存用量按需使用
    tf.config.set_visible_devices([gpu0],"GPU")

import tensorflow as tf
from tensorflow.keras import datasets, layers, models
import matplotlib.pyplot as plt

(train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()

# 将像素的值标准化至0到1的区间内。
train_images, test_images = train_images / 255.0, test_images / 255.0

train_images.shape,test_images.shape,train_labels.shape,test_labels.shape

plt.figure(figsize=(20,10))
for i in range(20):
    plt.subplot(5,10,i+1)
    plt.xticks([])
    plt.yticks([])
    plt.grid(False)
    plt.imshow(train_images[i], cmap=plt.cm.binary)
    plt.xlabel(train_labels[i])
plt.show()

#调整数据到我们需要的格式
train_images = train_images.reshape((60000, 28, 28, 1))
test_images = test_images.reshape((10000, 28, 28, 1))

# train_images, test_images = train_images / 255.0, test_images / 255.0

train_images.shape,test_images.shape,train_labels.shape,test_labels.shape

model = models.Sequential([
    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(28, 28, 1)),
    layers.MaxPooling2D((2, 2)),
    layers.Conv2D(64, (3, 3), activation='relu'),
    layers.MaxPooling2D((2, 2)),
    
    layers.Flatten(),
    layers.Dense(64, activation='relu'),
    layers.Dense(10)
])

model.summary()

model.compile(optimizer='adam',
              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),
              metrics=['accuracy'])

history = model.fit(train_images, train_labels, epochs=10, 
                    validation_data=(test_images, test_labels))

plt.imshow(test_images[1])

pre = model.predict(test_images)
pre[1]

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras import datasets, layers, models, callbacks, Input
import warnings

# 过滤无关警告
warnings.filterwarnings("ignore", category=UserWarning, module="keras.src.trainers.data_adapters.py_dataset_adapter")
warnings.filterwarnings("ignore", category=UserWarning, module="keras.src.layers.convolutional.base_conv")

# 设置中文字体（确保图表中文正常显示）
plt.rcParams["font.family"] = ["SimHei", "Microsoft YaHei"]  
plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题

# 模型保存路径
MODEL_PATH = os.path.join("model", "onedigit.keras")

# --------------------------
# 1. 模型创建与训练
# --------------------------
def create_and_train_model():
    # 首先检查是否存在已训练的模型
    model_loaded = False
    model = None
    train_mean = None
    
    # 检查模型文件是否存在
    if os.path.exists(MODEL_PATH):
        print(f"\n检测到已存在模型文件: {MODEL_PATH}")
        # 使用循环确保获取有效的用户输入
        while True:
            user_input = input("是否直接使用该模型进行识别？(y=使用/n=重新训练): ").strip().lower()
            if user_input in ['y', 'n']:
                if user_input == 'y':
                    try:
                        print(f"正在加载模型 {MODEL_PATH}...")
                        model = tf.keras.models.load_model(MODEL_PATH)
                        
                        # 加载测试数据评估模型
                        (_, _), (test_images, test_labels) = datasets.mnist.load_data()
                        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0
                        train_mean = np.mean(test_images)  # 使用测试集均值作为参考
                        test_images -= train_mean
                        
                        print("正在评估模型性能...")
                        test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)
                        print(f'\n已加载模型在测试集上的准确率: {test_acc:.4f}')
                        model_loaded = True
                        break
                    except Exception as e:
                        print(f"加载模型失败: {str(e)}")
                        retry = input("是否尝试重新加载？(y/n): ").strip().lower()
                        if retry != 'y':
                            print("将进行模型重新训练")
                            break
                else:  # 用户选择重新训练
                    print("用户选择重新训练模型")
                    break
            else:
                print("输入无效，请输入 'y' 或 'n'")
    
    # 如果没有成功加载模型，则进行训练
    if not model_loaded:
        # 确保模型保存目录存在
        os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
        
        # 加载MNIST数据集
        (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()
        
        # 数据预处理
        train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0
        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0
        train_mean = np.mean(train_images)
        train_images -= train_mean
        test_images -= train_mean
        
        # 增强易混淆数字的样本
        def augment_critical_digits(images, labels, target_digits, multiply=2):
            """针对性增强样本，提升识别准确率"""
            for digit in target_digits:
                mask = labels == digit
                target_imgs = images[mask]
                target_lbls = labels[mask]
                
                # 生成多样化增强样本
                datagen = tf.keras.preprocessing.image.ImageDataGenerator(
                    rotation_range=8,
                    width_shift_range=0.1,
                    height_shift_range=0.1,
                    zoom_range=0.15
                )
                augmented = next(datagen.flow(target_imgs, target_lbls, batch_size=len(target_imgs), shuffle=False))
                
                # 扩展样本
                for _ in range(multiply-1):
                    images = np.concatenate([images, augmented[0]])
                    labels = np.concatenate([labels, augmented[1]])
            return images, labels
        
        # 增强7和9的样本
        train_images, train_labels = augment_critical_digits(train_images, train_labels, [9, 7])
        
        # 通用数据增强
        datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rotation_range=12,
            width_shift_range=0.15,
            height_shift_range=0.15,
            zoom_range=0.2,
            shear_range=0.1,
            fill_mode='constant',
            cval=0.0
        )
        datagen.fit(train_images)
        
        # 模型结构
        model = models.Sequential([
            Input(shape=(28, 28, 1)),  # 明确的输入层定义
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.Conv2D(64, (1, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.Conv2D(128, (3, 1), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            
            layers.Flatten(),
            layers.Dense(128, activation='relu'),
            layers.Dropout(0.3),
            layers.Dense(10)
        ])
        
        # 编译模型
        optimizer = tf.keras.optimizers.Adam(
            learning_rate=0.001,
            weight_decay=1e-5
        )
        model.compile(
            optimizer=optimizer,
            loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),
            metrics=['accuracy']
        )
        
        # 训练回调
        callback_list = [
            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=3),
            callbacks.EarlyStopping(monitor='val_accuracy', patience=6, restore_best_weights=True),
            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)
        ]
        
        # 训练模型
        print("\n开始训练新模型...")
        model.fit(
            datagen.flow(train_images, train_labels, batch_size=64),
            epochs=5,  # 可根据需要调整训练轮数
            validation_data=(test_images, test_labels),
            callbacks=callback_list
        )
        
        # 加载最佳模型
        if os.path.exists(MODEL_PATH):
            model = tf.keras.models.load_model(MODEL_PATH)
            test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)
            print(f'\n模型在测试集上的最佳准确率: {test_acc:.4f}')
        else:
            print("\n警告：未找到最佳模型文件，使用最后训练的模型")
    
    return model, train_mean

# --------------------------
# 2. 图片预处理（兼容中文文件名）
# --------------------------
def load_and_preprocess_images(directory, train_mean):
    images = []
    original_images = []
    processed_images = []
    filenames = []
    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')
    
    if not os.path.exists(directory):
        print(f"错误：目录 '{directory}' 不存在！")
        return np.array([]), np.array([]), np.array([]), []
    
    # 读取目录下所有文件，确保中文文件名编码正确
    try:
        all_files = os.listdir(directory)
    except UnicodeDecodeError:
        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))
    
    # 筛选图片文件（支持中文文件名）
    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]
    print(f"找到图片文件：{image_files}")

    for filename in image_files:
        img_path = os.path.join(directory, filename)
        
        try:
            # 读取图片（支持中文路径）
            img_data = np.fromfile(img_path, dtype=np.uint8)
            img = cv2.imdecode(img_data, cv2.IMREAD_GRAYSCALE)
        except Exception as e:
            print(f"无法读取 {filename}：{str(e)}")
            continue
        
        if img is None:
            print(f"警告：{filename} 可能损坏或格式不支持")
            continue
        
        # 保存原始图片（缩放后便于显示）
        original_images.append(cv2.resize(img, (200, 200)))
        
        # 预处理流程
        img_scaled = cv2.resize(img, (56, 56), interpolation=cv2.INTER_CUBIC)
        img_resized = cv2.resize(img_scaled, (28, 28))
        
        _, img_thresh = cv2.threshold(
            img_resized, 80, 255,
            cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU
        )
        
        kernel = np.ones((1, 1), np.uint8)
        img_clean = cv2.morphologyEx(img_thresh, cv2.MORPH_OPEN, kernel)
        
        contours, _ = cv2.findContours(img_clean, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            max_contour = max(contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(max_contour)
            digit_roi = img_clean[y:y+h, x:x+w]
            
            scale = min(22/w, 22/h)
            resized_digit = cv2.resize(digit_roi, (int(w*scale), int(h*scale)))
            padded_digit = np.zeros((28, 28), dtype=np.uint8)
            y_offset = (28 - resized_digit.shape[0]) // 2
            x_offset = (28 - resized_digit.shape[1]) // 2
            padded_digit[y_offset:y_offset+resized_digit.shape[0], 
                         x_offset:x_offset+resized_digit.shape[1]] = resized_digit
        else:
            padded_digit = img_clean
        
        normalized_img = padded_digit / 255.0 - train_mean
        processed_images.append(padded_digit)
        images.append(normalized_img)
        filenames.append(filename)
        print(f"成功处理：{filename}")

    return np.array(images), np.array(original_images), np.array(processed_images), filenames

# --------------------------
# 3. 预测函数
# --------------------------
def predict_digits(model, images):
    logits = model.predict(images, verbose=0)
    
    def softmax(x):
        """数值稳定的softmax实现"""
        x_max = np.max(x, axis=1, keepdims=True)
        e_x = np.exp(x - x_max)
        return e_x / e_x.sum(axis=1, keepdims=True)
    
    probabilities = softmax(logits)
    return probabilities, np.argmax(probabilities, axis=1), np.max(probabilities, axis=1)*100

# --------------------------
# 4. 主程序
# --------------------------
def main():
    print("正在准备模型...")
    model, train_mean = create_and_train_model()
    
    image_dir = "numbers"  # 存放图片的文件夹（支持中文路径）
    images, original_images, processed_images, filenames = load_and_preprocess_images(image_dir, train_mean)

    if len(images) > 0:
        model_input = images.reshape(-1, 28, 28, 1)
        probabilities, predicted_digits, confidence = predict_digits(model, model_input)
        
        for i in range(len(images)):
            plt.figure(figsize=(12, 4))
            plt.subplot(131)
            plt.imshow(original_images[i], cmap='gray')
            plt.title(f"原始图片：{filenames[i]}")
            plt.axis('off')
            
            plt.subplot(132)
            plt.imshow(processed_images[i], cmap='gray')
            plt.title("处理后（模型输入）")
            plt.axis('off')
            
            plt.subplot(133)
            plt.bar(range(10), probabilities[i], color='skyblue')
            plt.xticks(range(10))
            plt.title(f"预测：{predicted_digits[i]}（置信度：{confidence[i]:.1f}%）")
            plt.xlabel("数字")
            plt.ylabel("概率")
            
            plt.tight_layout()
            plt.show()
            
            # 打印结果
            print(f"\n图片 {filenames[i]} 分析：")
            print(f"预测数字：{predicted_digits[i]}（置信度：{confidence[i]:.1f}%）")
            top2 = np.argsort(probabilities[i])[-2:][::-1]
            print(f"Top 2 可能的数字：{top2[0]}（{probabilities[i][top2[0]]*100:.1f}%）、{top2[1]}（{probabilities[i][top2[1]]*100:.1f}%）")
            print("-" * 60)
    else:
        print("没有找到可处理的图片文件")

if __name__ == "__main__":
    main()
